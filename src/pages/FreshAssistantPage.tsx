import { useState, useEffect } from 'react'
import { <PERSON>, Bo<PERSON>, User, <PERSON>rk<PERSON> } from 'lucide-react'
import { supabase } from '../lib/supabase'
import { useAuth } from '../contexts/AuthContext'
import { callOpenRouter } from '../lib/openrouter'

interface QuickAction {
  id: string
  title: string
  description: string
  action_type: string
  display_order: number
}

// AI Chat service integration using OpenRouter with Google Gemini 2.0 Flash
const getAIChatResponse = async (userMessage: string, userId?: string): Promise<string> => {
  try {
    // RULE 0001: Real AI service integration using OpenRouter API
    const aiResponse = await callOpenRouter(userMessage)

    // Store conversation in database for user history
    if (userId) {
      await supabase
        .from('ai_conversations')
        .insert({
          user_id: userId,
          user_input: userMessage,
          ai_response: aiResponse,
          created_at: new Date().toISOString(),
          session_type: 'fresh_assistant_page'
        })
    }

    return aiResponse
  } catch (error) {
    console.error('AI service error:', error)
    return "I'm here to support your quit smoking journey. While I'm experiencing some technical difficulties, remember that you have the strength to overcome any challenge. What specific support do you need right now?"
  }
}

export default function FreshAssistantPage() {
  const { user } = useAuth()
  // RULE 0001: No hardcoded messages - start with empty state
  const [messages, setMessages] = useState<any[]>([])
  const [inputMessage, setInputMessage] = useState('')
  const [loading, setLoading] = useState(false)
  
  // RULE 0001: Dynamic quick actions from database - hardcoded array removed
  const [quickActions, setQuickActions] = useState<QuickAction[]>([])
  const [actionsLoading, setActionsLoading] = useState(true)
  
  useEffect(() => {
    fetchQuickActions()
  }, [])

  const fetchQuickActions = async () => {
    try {
      const { data, error } = await supabase
        .from('quick_actions')
        .select('*')
        .order('display_order', { ascending: true })
      
      if (error) {
        console.error('Error fetching quick actions:', error)
        // No fallback data per Holy Rule #1
        setQuickActions([])
      } else {
        setQuickActions(data || [])
      }
    } catch (err) {
      console.error('Error fetching quick actions:', err)
      setQuickActions([])
    } finally {
      setActionsLoading(false)
    }
  }

  const handleSendMessage = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!inputMessage.trim() || loading) return

    const userMessage = {
      id: Date.now(),
      type: 'user',
      content: inputMessage
    }

    setMessages([...messages, userMessage])
    setInputMessage('')
    setLoading(true)

    try {
      // RULE 0001: Real AI service integration - no hardcoded responses
      const aiResponseText = await getAIChatResponse(inputMessage, user?.id)
      
      const aiResponse = {
        id: Date.now() + 1,
        type: 'bot',
        content: aiResponseText
      }
      
      setMessages(prev => [...prev, aiResponse])
    } catch (error) {
      console.error('Error getting AI response:', error)
      const errorResponse = {
        id: Date.now() + 1,
        type: 'bot',
        content: "I'm sorry, I'm having trouble responding right now. Please try again in a moment."
      }
      setMessages(prev => [...prev, errorResponse])
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="min-h-screen bg-background">
      <div className="max-w-6xl mx-auto px-6 lg:px-8 py-12">
        {/* Header */}
        <div className="text-center mb-16">
          <div className="flex items-center justify-center gap-6 mb-8">
            <div className="w-24 h-24 bg-primary rounded-3xl flex items-center justify-center shadow-md">
              <Sparkles className="w-12 h-12 text-primary-foreground" strokeWidth={2.5} />
            </div>
            <h1 className="typography-display-large font-light text-foreground tracking-tight">Fresh Assistant</h1>
          </div>
                      <p className="typography-headline-small text-muted-foreground font-light max-w-3xl mx-auto leading-relaxed">
            Your AI-powered wellness companion, available 24/7 for personalized support and guidance.
          </p>
        </div>

        {/* Chat Container */}
                  <div className="bg-card rounded-3xl shadow-md border border-border overflow-hidden">
          {/* Messages */}
          <div className="h-[500px] overflow-y-auto p-8 space-y-6">
            {messages.map((message) => (
              <div
                key={message.id}
                className={`flex items-start gap-4 ${
                  message.type === 'user' ? 'flex-row-reverse' : ''
                }`}
              >
                <div className={`w-12 h-12 rounded-2xl flex items-center justify-center flex-shrink-0 shadow-md ${
                  message.type === 'user'
                                    ? 'bg-primary text-primary-foreground'
                : 'bg-muted text-muted-foreground'
                }`}>
                  {message.type === 'user' ? (
                    <User className="w-6 h-6" strokeWidth={2} />
                  ) : (
                    <Bot className="w-6 h-6" strokeWidth={2} />
                  )}
                </div>
                <div className={`max-w-md px-6 py-4 rounded-2xl ${
                  message.type === 'user'
                    ? 'bg-primary text-primary-foreground shadow-md'
                    : 'bg-muted text-foreground border border-border'
                }`}>
                  <p className="typography-body-medium leading-relaxed font-light">{message.content}</p>
                </div>
              </div>
            ))}
          </div>

          {/* Input Form */}
          <div className="border-t border-border p-8">
            <form onSubmit={handleSendMessage} className="flex gap-4">
              <input
                type="text"
                value={inputMessage}
                onChange={(e) => setInputMessage(e.target.value)}
                placeholder="Ask me anything about quitting smoking..."
                className="flex-1 px-6 py-4 border border-border rounded-2xl focus:ring-4 focus:ring-primary/20 focus:border-primary bg-background text-foreground font-light shadow-sm"
              />
              <button
                type="submit"
                className="inline-flex items-center gap-3 px-8 py-4 bg-primary text-primary-foreground font-medium rounded-2xl shadow-md hover:bg-primary/90 hover:shadow-lg focus:outline-none focus:ring-4 focus:ring-primary/20 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200"
                disabled={!inputMessage.trim()}
              >
                <Send className="w-5 h-5" strokeWidth={2} />
                <span className="hidden sm:inline">Send</span>
              </button>
            </form>
          </div>
        </div>

        {/* Quick Actions */}
        <div className="mt-12 grid sm:grid-cols-2 lg:grid-cols-3 gap-6">
          {actionsLoading ? (
            <div className="col-span-full text-center py-8">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
              <p className="text-muted-foreground typography-body-large font-light">Loading quick actions...</p>
            </div>
          ) : (
            quickActions.map((action) => (
              <button key={action.id} className="bg-card border border-border rounded-3xl p-8 text-left hover:shadow-lg hover:border-primary/30 focus:outline-none focus:ring-4 focus:ring-primary/20 transition-all duration-200 shadow-md h-full flex flex-col">
                <div className="font-medium text-foreground mb-4 typography-headline-small">{action.title}</div>
                <div className="text-muted-foreground font-light leading-relaxed">{action.description}</div>
              </button>
            ))
          )}
        </div>
      </div>
    </div>
  )
}
