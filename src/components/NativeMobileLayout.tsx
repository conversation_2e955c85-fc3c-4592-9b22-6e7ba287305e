import React, { useEffect, useState } from 'react'
import { Capacitor } from '@capacitor/core'
import { StatusBar, Style } from '@capacitor/status-bar'
import { useLocation, useNavigate } from 'react-router-dom'
import { Home, TrendingUp, Users, Heart, User } from 'lucide-react'

interface NativeMobileLayoutProps {
  children: React.ReactNode
}

const NativeMobileLayout: React.FC<NativeMobileLayoutProps> = ({
  children
}) => {
  const [safeAreaInsets, _setSafeAreaInsets] = useState({
    top: 0,
    bottom: 0,
    left: 0,
    right: 0
  })
  const [isNative, setIsNative] = useState(false)
  const location = useLocation()
  const navigate = useNavigate()
  
  // Real iOS tab bar configuration with actual webapp routes
  const tabBarItems = [
    {
      id: 'dashboard',
      label: 'Home',
      icon: Home,
      path: '/dashboard',
      isActive: location.pathname === '/dashboard'
    },
    {
      id: 'progress',
      label: 'Progress',
      icon: TrendingUp,
      path: '/dashboard/progress',
      isActive: location.pathname === '/dashboard/progress'
    },
    {
      id: 'community',
      label: 'Community',
      icon: Users,
      path: '/dashboard/community',
      isActive: location.pathname === '/dashboard/community'
    },
    {
      id: 'wellness',
      label: 'Wellness',
      icon: Heart,
      path: '/dashboard/mood',
      isActive: location.pathname.includes('/dashboard/mood') || location.pathname.includes('/dashboard/breathing')
    },
    {
      id: 'settings',
      label: 'Settings',
      icon: User,
      path: '/dashboard/settings',
      isActive: location.pathname === '/dashboard/settings'
    }
  ]

  useEffect(() => {
    const setupNativeLayout = async () => {
      // Enable native layout for both real native apps AND mobile preview
      const isMobileViewport = window.innerWidth <= 820 || window.innerHeight <= 1024
      const isRealNative = Capacitor.isNativePlatform()

      // Force mobile layout for testing - always enable for mobile viewports
      console.log('🔍 Mobile Detection:', {
        innerWidth: window.innerWidth,
        innerHeight: window.innerHeight,
        isMobileViewport,
        isRealNative
      })

      if (isRealNative || isMobileViewport) {
        console.log('✅ Enabling native mobile layout')
        setIsNative(true)
        
        // Only configure Capacitor features for real native apps
        if (isRealNative) {
          try {
            // Safe area handled via CSS variables by @capacitor-community/safe-area
            // No API call needed - plugin automatically sets CSS variables
            
            // Configure status bar for native feel
            if (Capacitor.getPlatform() === 'ios') {
              await StatusBar.setStyle({ style: Style.Light })
              await StatusBar.setBackgroundColor({ color: '#000000' })
            } else {
              await StatusBar.setStyle({ style: Style.Dark })
              await StatusBar.setBackgroundColor({ color: '#22C55E' })
            }
          } catch (error) {
            console.log('Safe area or status bar not available:', error)
          }
        }
      }
    }

    setupNativeLayout()
  }, [])

  const platform = Capacitor.getPlatform()
  const isIOS = platform === 'ios'
  const isAndroid = platform === 'android'

  // Debug logging
  console.log('🔍 NativeMobileLayout render:', { isNative, platform, isIOS, isAndroid })

  return (
    <div className={`min-h-screen bg-background ${isNative ? 'native-app-layout' : ''} ${isIOS ? 'ios-layout' : ''} ${isAndroid ? 'android-layout' : ''}`} style={{paddingTop: isNative ? `${safeAreaInsets.top}px` : '0'}}>
      {/* iOS-style Navigation Header */}
      {isNative && (
        <div className={`fixed top-0 left-0 right-0 z-50 ${isIOS ? 'bg-primary' : 'bg-primary'} ${isIOS ? 'ios-status-bar' : 'android-status-bar'}`} style={{paddingTop: `${safeAreaInsets.top}px`, paddingLeft: `${safeAreaInsets.left}px`, paddingRight: `${safeAreaInsets.right}px`}}>
          <div className="flex items-center justify-between h-14 px-4">
            {/* Menu Button - iOS Style */}
            <button className="flex items-center justify-center w-10 h-10 rounded-full bg-white/20 backdrop-blur-sm border border-white/30 shadow-lg">
              <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
              </svg>
            </button>
            
            {/* App Title */}
            <h1 className="text-lg font-semibold text-white">Mission Fresh</h1>
            
            {/* Profile/Settings Button */}
            <button className="flex items-center justify-center w-10 h-10 rounded-full bg-white/20 backdrop-blur-sm border border-white/30 shadow-lg">
              <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
              </svg>
            </button>
          </div>
        </div>
      )}
      
      {/* Main Content */}
      <main className={`${isNative ? 'pt-14' : ''} px-4 pb-20`}>
        {children}
      </main>
      
      {/* Real iOS Bottom Tab Bar */}
      {isNative && (
        <div
          className="fixed bottom-0 left-0 right-0 bg-white/95 backdrop-blur-xl border-t border-black/8 shadow-xl z-50"
          style={{paddingBottom: `${safeAreaInsets.bottom}px`, paddingLeft: `${safeAreaInsets.left}px`, paddingRight: `${safeAreaInsets.right}px`}}
          data-testid="mobile-bottom-nav"
        >
          <div className="flex items-center justify-around py-2 px-4">
            {tabBarItems.map((item) => {
              const Icon = item.icon
              return (
                <button
                  key={item.id}
                  onClick={() => navigate(item.path)}
                  className={`flex flex-col items-center justify-center py-2 px-3 rounded-lg transition-all duration-200 min-w-0 flex-1 ${
                    item.isActive 
                      ? 'text-primary bg-primary/10' 
                      : 'text-gray-600 hover:text-gray-900 active:bg-gray-100'
                  }`}
                >
                  <Icon className={`w-6 h-6 mb-1 ${
                    item.isActive ? 'text-primary' : 'text-gray-600'
                  }`} />
                  <span className={`text-xs font-medium truncate ${
                    item.isActive ? 'text-primary' : 'text-gray-600'
                  }`}>
                    {item.label}
                  </span>
                </button>
              )
            })}
          </div>
        </div>
      )}
    </div>
  )
}



export default NativeMobileLayout
