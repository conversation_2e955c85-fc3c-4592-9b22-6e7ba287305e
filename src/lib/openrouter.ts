// OpenRouter API integration for Fresh Assistant
// HOLY RULE 0001: Real AI service integration using OpenRouter with Google Gemini 2.0 Flash

export interface OpenRouterConfig {
  apiKey: string
  model: string
  baseUrl: string
}

export interface ChatMessage {
  role: 'system' | 'user' | 'assistant'
  content: string
}

export interface OpenRouterResponse {
  choices: Array<{
    message: {
      content: string
      role: string
    }
    finish_reason: string
  }>
  usage: {
    prompt_tokens: number
    completion_tokens: number
    total_tokens: number
  }
}

// OpenRouter configuration for Mission Fresh
const OPENROUTER_CONFIG: OpenRouterConfig = {
  apiKey: '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',
  model: 'google/gemini-2.0-flash-001',
  baseUrl: 'https://openrouter.ai/api/v1'
}

// System prompt for Fresh Assistant
const FRESH_ASSISTANT_SYSTEM_PROMPT = `You are <PERSON><PERSON><PERSON>, the compassionate and knowledgeable quit smoking coach for Mission Fresh app. You provide evidence-based, personalized support to help people quit smoking and maintain a smoke-free lifestyle.

Your expertise includes:
- Nicotine addiction science and withdrawal management
- Behavioral change strategies and habit replacement
- Stress management and emotional regulation techniques
- Relapse prevention and recovery strategies
- Health benefits timeline and motivation
- NRT (Nicotine Replacement Therapy) guidance
- Mindfulness and breathing techniques
- Support system building

Guidelines:
- Always be empathetic, non-judgmental, and encouraging
- Provide practical, actionable advice
- Reference scientific evidence when appropriate
- Acknowledge that quitting is challenging but achievable
- Offer multiple strategies to choose from
- Ask follow-up questions to better understand their situation
- Celebrate small wins and progress
- Provide crisis support for strong cravings

Respond in a warm, professional tone as a trusted wellness coach. Keep responses concise but comprehensive, typically 2-4 sentences. Always end with an encouraging question or suggestion to keep the conversation going.`

export async function callOpenRouter(userMessage: string): Promise<string> {
  try {
    console.log('🤖 Calling OpenRouter API with Gemini 2.0 Flash...')
    
    const messages: ChatMessage[] = [
      {
        role: 'system',
        content: FRESH_ASSISTANT_SYSTEM_PROMPT
      },
      {
        role: 'user',
        content: userMessage
      }
    ]

    const response = await fetch(`${OPENROUTER_CONFIG.baseUrl}/chat/completions`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${OPENROUTER_CONFIG.apiKey}`,
        'Content-Type': 'application/json',
        'HTTP-Referer': 'https://missionfresh.app',
        'X-Title': 'Mission Fresh - Quit Smoking Assistant'
      },
      body: JSON.stringify({
        model: OPENROUTER_CONFIG.model,
        messages: messages,
        temperature: 0.7,
        max_tokens: 1024,
        top_p: 0.95,
        frequency_penalty: 0,
        presence_penalty: 0
      })
    })

    if (!response.ok) {
      throw new Error(`OpenRouter API error: ${response.status} ${response.statusText}`)
    }

    const data: OpenRouterResponse = await response.json()
    
    if (!data.choices || data.choices.length === 0) {
      throw new Error('No response from OpenRouter API')
    }

    const aiResponse = data.choices[0].message.content
    
    if (!aiResponse) {
      throw new Error('Empty response from OpenRouter API')
    }

    console.log('✅ OpenRouter API call successful')
    return aiResponse

  } catch (error) {
    console.error('❌ OpenRouter API error:', error)
    
    // Fallback response for quit smoking support
    return "I understand you're looking for support with your quit smoking journey. While I'm experiencing some technical difficulties right now, remember that cravings are temporary and you have the strength to overcome them. Try taking 10 deep breaths, drinking a glass of water, or going for a short walk. What specific challenge are you facing today?"
  }
}

// Test function for OpenRouter integration
export async function testOpenRouterIntegration(): Promise<void> {
  console.log('🧪 Testing OpenRouter integration...')
  
  try {
    const testMessage = "I'm having a strong craving to smoke. What should I do?"
    const response = await callOpenRouter(testMessage)
    
    console.log('✅ Test successful!')
    console.log('📝 Test response:', response)
    
  } catch (error) {
    console.error('❌ Test failed:', error)
  }
}
