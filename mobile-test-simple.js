const puppeteer = require('puppeteer');

(async () => {
  console.log('🔥 SIMPLE MOBILE TEST - CHECKING BOTTOM BAR 🔥');
  
  const browser = await puppeteer.launch({
    headless: false,
    defaultViewport: null,
    args: ['--start-maximized', '--disable-web-security']
  });
  
  const page = await browser.newPage();
  
  // iPhone 12 Pro emulation
  await page.emulate({
    name: 'iPhone 12 Pro',
    userAgent: 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_4 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.0.3 Mobile/15E148 Safari/604.1',
    viewport: {
      width: 390,
      height: 844,
      deviceScaleFactor: 3,
      isMobile: true,
      hasTouch: true,
      isLandscape: false
    }
  });
  
  console.log('📱 Loading mobile app...');
  await page.goto('http://localhost:5005', { waitUntil: 'networkidle2' });
  
  // Take screenshot
  await page.screenshot({ path: 'mobile-test-homepage.png', fullPage: true });
  
  // Check mobile detection
  const mobileCheck = await page.evaluate(() => {
    return {
      windowSize: { width: window.innerWidth, height: window.innerHeight },
      isMobileViewport: window.innerWidth <= 820 || window.innerHeight <= 1024,
      hasCapacitor: !!window.Capacitor,
      currentPath: window.location.pathname,
      hasNativeLayout: !!document.querySelector('.native-app-layout'),
      hasBottomNav: !!document.querySelector('.fixed.bottom-0'),
      allBottomElements: Array.from(document.querySelectorAll('*')).filter(el => 
        el.className && el.className.includes && el.className.includes('bottom')
      ).map(el => ({
        tagName: el.tagName,
        className: el.className,
        visible: el.offsetWidth > 0 && el.offsetHeight > 0
      }))
    };
  });
  
  console.log('📊 MOBILE CHECK RESULTS:', JSON.stringify(mobileCheck, null, 2));
  
  // Navigate to dashboard
  console.log('📱 Navigating to dashboard...');
  await page.goto('http://localhost:5005/dashboard', { waitUntil: 'networkidle2' });
  await page.screenshot({ path: 'mobile-test-dashboard.png', fullPage: true });
  
  // Check dashboard mobile layout
  const dashboardCheck = await page.evaluate(() => {
    return {
      currentPath: window.location.pathname,
      hasNativeLayout: !!document.querySelector('.native-app-layout'),
      hasBottomNav: !!document.querySelector('.fixed.bottom-0'),
      tabBarItems: Array.from(document.querySelectorAll('button')).filter(btn => 
        btn.textContent && (
          btn.textContent.includes('Home') || 
          btn.textContent.includes('Progress') || 
          btn.textContent.includes('Community') ||
          btn.textContent.includes('Wellness') ||
          btn.textContent.includes('Settings')
        )
      ).map(btn => ({
        text: btn.textContent.trim(),
        visible: btn.offsetWidth > 0 && btn.offsetHeight > 0,
        className: btn.className
      }))
    };
  });
  
  console.log('📊 DASHBOARD CHECK RESULTS:', JSON.stringify(dashboardCheck, null, 2));
  
  // Test clicking on tab bar items if they exist
  if (dashboardCheck.tabBarItems.length > 0) {
    console.log('📱 Testing tab bar navigation...');
    
    // Try clicking on Progress tab
    const progressButton = await page.$('button:contains("Progress")');
    if (progressButton) {
      await progressButton.click();
      await page.waitForTimeout(2000);
      await page.screenshot({ path: 'mobile-test-progress-tab.png', fullPage: true });
    }
  }
  
  console.log('✅ Mobile test complete - check screenshots!');
  
  // Keep browser open for manual inspection
  console.log('🔍 Browser staying open for manual inspection...');
  await new Promise(() => {}); // Keep running
})().catch(console.error);
