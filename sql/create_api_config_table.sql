-- Create API configuration table for Mission Fresh
-- HOLY RULE 0001: Store API keys securely in database, not hardcoded

-- Create api_config table in mission_fresh schema
CREATE TABLE IF NOT EXISTS mission_fresh.api_config (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    service_name VARCHAR(100) NOT NULL UNIQUE, -- 'openrouter', 'gemini', etc.
    api_key TEXT NOT NULL, -- Encrypted API key
    model_name VARCHAR(200), -- Model to use (e.g., 'google/gemini-2.0-flash-001')
    base_url TEXT, -- API base URL
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Insert OpenRouter configuration
INSERT INTO mission_fresh.api_config (service_name, api_key, model_name, base_url, is_active)
VALUES (
    'openrouter',
    '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',
    'google/gemini-2.0-flash-001',
    'https://openrouter.ai/api/v1',
    true
) ON CONFLICT (service_name) DO UPDATE SET
    api_key = EXCLUDED.api_key,
    model_name = EXCLUDED.model_name,
    base_url = EXCLUDED.base_url,
    is_active = EXCLUDED.is_active,
    updated_at = NOW();

-- Create index for faster lookups
CREATE INDEX IF NOT EXISTS idx_api_config_service_active 
ON mission_fresh.api_config (service_name, is_active);

-- Enable RLS (Row Level Security)
ALTER TABLE mission_fresh.api_config ENABLE ROW LEVEL SECURITY;

-- Create policy to allow service role to access API configs
CREATE POLICY "Service role can access API configs" ON mission_fresh.api_config
    FOR ALL USING (auth.role() = 'service_role');

-- Grant permissions to service role
GRANT ALL ON mission_fresh.api_config TO service_role;
GRANT USAGE ON SCHEMA mission_fresh TO service_role;
