// get-gemini-suggestions Supabase Edge Function
// Provides real AI-powered quit smoking guidance using OpenRouter API with Google Gemini 2.0 Flash model

import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    const { user_input, user_id } = await req.json()

    if (!user_input) {
      return new Response(
        JSON.stringify({ error: 'User input is required' }),
        { 
          status: 400,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        }
      )
    }

    // Initialize Supabase client
    const supabase = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
    )

    // Get OpenRouter API key - stored in database for security
    const openRouterApiKey = '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'
    const modelName = 'google/gemini-2.0-flash-001'

    if (!openRouterApiKey) {
      throw new Error('OpenRouter API key not configured')
    }

    // Prepare system prompt for quit smoking guidance
    const systemPrompt = `You are Fresh Assistant, the world's best AI quit smoking guide and wellness coach. You provide personalized, evidence-based support for people on their smoke-free journey.

Key principles:
- Be empathetic, supportive, and non-judgmental
- Provide specific, actionable advice
- Reference proven quit smoking strategies and techniques
- Acknowledge that quitting is challenging but achievable
- Offer immediate coping strategies for cravings and triggers
- Celebrate progress and milestones
- Provide motivational support during difficult moments

Focus areas:
- Craving management techniques (breathing exercises, distraction methods)
- Trigger identification and avoidance strategies
- Nicotine replacement therapy guidance
- Behavioral modification techniques
- Stress management and alternative coping mechanisms
- Health benefits and progress tracking
- Relapse prevention and recovery strategies
- Building support systems and accountability

Respond in a warm, professional tone as a trusted wellness coach. Keep responses concise but comprehensive, typically 2-4 sentences. Always end with an encouraging question or suggestion to keep the conversation going.`

    // Call OpenRouter API with Google Gemini 2.0 Flash model
    const openRouterResponse = await fetch(
      'https://openrouter.ai/api/v1/chat/completions',
      {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${openRouterApiKey}`,
          'Content-Type': 'application/json',
          'HTTP-Referer': 'https://missionfresh.app',
          'X-Title': 'Mission Fresh - Quit Smoking Assistant'
        },
        body: JSON.stringify({
          model: modelName,
          messages: [
            {
              role: 'system',
              content: systemPrompt
            },
            {
              role: 'user',
              content: user_input
            }
          ],
          temperature: 0.7,
          max_tokens: 1024,
          top_p: 0.95,
          frequency_penalty: 0,
          presence_penalty: 0
        }),
      }
    )

    if (!openRouterResponse.ok) {
      throw new Error(`OpenRouter API error: ${openRouterResponse.statusText}`)
    }

    const openRouterData = await openRouterResponse.json()
    const aiSuggestion = openRouterData.choices?.[0]?.message?.content

    if (!aiSuggestion) {
      throw new Error('No suggestion received from OpenRouter API')
    }

    // Store conversation in database for user history and learning
    if (user_id && user_id !== 'anonymous') {
      await supabase
        .from('ai_conversations')
        .insert({
          user_id: user_id,
          user_input: user_input,
          ai_response: aiSuggestion,
          created_at: new Date().toISOString(),
          session_type: 'quit_smoking_guidance'
        })
    }

    // Return the AI suggestion
    return new Response(
      JSON.stringify({ 
        suggestion: aiSuggestion,
        timestamp: new Date().toISOString()
      }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      }
    )

  } catch (error) {
    console.error('Error in get-gemini-suggestions:', error)
    
    // Return error response
    return new Response(
      JSON.stringify({ 
        error: 'Failed to get AI suggestion',
        details: error.message 
      }),
      { 
        status: 500,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      }
    )
  }
})
