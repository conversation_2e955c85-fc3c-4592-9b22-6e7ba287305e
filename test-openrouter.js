// Test OpenRouter integration with Google Gemini 2.0 Flash model
// HOLY RULE 0001: Real API testing, not hardcoded responses

async function testOpenRouter() {
  console.log('🤖 Testing OpenRouter API with Google Gemini 2.0 Flash...')
  
  const apiKey = '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'
  const model = 'google/gemini-2.0-flash-001'
  
  const systemPrompt = `You are FreshAI, the compassionate quit smoking coach for Mission Fresh app. Provide evidence-based, personalized support to help people quit smoking. Be empathetic, encouraging, and offer practical advice.`
  
  const userMessage = "I'm having a strong craving to smoke right now. What should I do?"
  
  try {
    const response = await fetch('https://openrouter.ai/api/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${apiKey}`,
        'Content-Type': 'application/json',
        'HTTP-Referer': 'https://missionfresh.app',
        'X-Title': 'Mission Fresh - Quit Smoking Assistant'
      },
      body: JSON.stringify({
        model: model,
        messages: [
          {
            role: 'system',
            content: systemPrompt
          },
          {
            role: 'user',
            content: userMessage
          }
        ],
        temperature: 0.7,
        max_tokens: 1024,
        top_p: 0.95
      })
    })
    
    if (!response.ok) {
      throw new Error(`API error: ${response.status} ${response.statusText}`)
    }
    
    const data = await response.json()
    
    if (data.choices && data.choices.length > 0) {
      const aiResponse = data.choices[0].message.content
      console.log('✅ OpenRouter API test successful!')
      console.log('📝 AI Response:', aiResponse)
      console.log('💰 Usage:', data.usage)
    } else {
      console.error('❌ No response from API')
    }
    
  } catch (error) {
    console.error('❌ OpenRouter API test failed:', error)
  }
}

testOpenRouter()
