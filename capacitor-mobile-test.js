const puppeteer = require('puppeteer');

(async () => {
  console.log('🔥 CAPACITOR MOBILE APP VISUAL TEST - PORT 5005 🔥');
  
  const browser = await puppeteer.launch({
    headless: false,
    defaultViewport: null,
    args: ['--start-maximized', '--disable-web-security']
  });
  
  const page = await browser.newPage();
  
  // iPhone 12 Pro mobile emulation for Capacitor app
  await page.emulate({
    name: 'iPhone 12 Pro',
    userAgent: 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_4 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.0.3 Mobile/15E148 Safari/604.1',
    viewport: {
      width: 390,
      height: 844,
      deviceScaleFactor: 3,
      isMobile: true,
      hasTouch: true,
      isLandscape: false
    }
  });
  
  console.log('📱 Loading Capacitor mobile app...');
  await page.goto('http://localhost:5005', { waitUntil: 'networkidle2' });
  
  // Screenshot 1: Homepage
  await page.screenshot({ path: 'mobile-capacitor-homepage.png', fullPage: true });
  console.log('📸 Homepage screenshot taken');
  
  // Check mobile status
  const status = await page.evaluate(() => ({
    viewport: { width: window.innerWidth, height: window.innerHeight },
    hasCapacitor: !!window.Capacitor,
    currentPath: window.location.pathname,
    hasBottomNav: !!document.querySelector('.fixed.bottom-0'),
    buttonCount: document.querySelectorAll('button').length
  }));
  
  console.log('📊 Mobile Status:', JSON.stringify(status, null, 2));
  
  // Navigate to dashboard
  console.log('📱 Navigating to dashboard...');
  await page.goto('http://localhost:5005/dashboard', { waitUntil: 'networkidle2' });
  await page.waitForTimeout(3000);
  
  // Screenshot 2: Dashboard
  await page.screenshot({ path: 'mobile-capacitor-dashboard.png', fullPage: true });
  console.log('📸 Dashboard screenshot taken');
  
  // Check for bottom navigation
  const dashboardStatus = await page.evaluate(() => ({
    currentPath: window.location.pathname,
    hasBottomNav: !!document.querySelector('[data-testid="mobile-bottom-nav"]'),
    tabButtons: Array.from(document.querySelectorAll('button')).filter(btn => 
      btn.textContent && (
        btn.textContent.includes('Home') || 
        btn.textContent.includes('Progress') || 
        btn.textContent.includes('Community')
      )
    ).length
  }));
  
  console.log('📊 Dashboard Status:', JSON.stringify(dashboardStatus, null, 2));
  
  // Test tab navigation if bottom nav exists
  if (dashboardStatus.hasBottomNav) {
    console.log('✅ Bottom navigation found! Testing tabs...');
    
    // Try clicking Progress tab
    try {
      const progressBtn = await page.$('button:contains("Progress")');
      if (progressBtn) {
        await progressBtn.click();
        await page.waitForTimeout(2000);
        await page.screenshot({ path: 'mobile-capacitor-progress.png', fullPage: true });
        console.log('📸 Progress tab screenshot taken');
      }
    } catch (e) {
      console.log('⚠️ Progress tab click failed:', e.message);
    }
  } else {
    console.log('❌ Bottom navigation not found');
  }
  
  console.log('🔥 CAPACITOR MOBILE TEST COMPLETE! 🔥');
  console.log('📸 Check screenshots: mobile-capacitor-*.png');
  
  // Keep browser open for manual inspection
  console.log('🔍 Browser staying open for manual testing...');
  await new Promise(() => {}); // Keep running
})().catch(console.error);
